# 平衡车控制系统学习笔记

## 1. 硬件组成

### 1.1 核心硬件
- **两个电机 + 电机驱动**：提供动力和精确控制
- **陀螺仪（IMU）**：测量角度和角速度
- **STM32微控制器**：核心控制单元

## 2. 控制理论基础

### 2.1 基本原理
要想小车保持平衡且静止，需要让小车<span style="color:red">**先平衡后静止**</span>。

### 2.2 双环控制系统
控制系统包含两个关键环节：
- <span style="color:red">**直立环（姿态环）**</span>：内环，负责保持小车直立
- <span style="color:red">**速度环**</span>：外环，负责控制小车运动速度

## 3. 控制系统原理详解

### 3.1 形象理解：手掌托棍子
想象用手掌托起一根棍子让它保持竖直，这与平衡小车控制原理完全相同：

- **目标**：让棍子保持竖直不倒
- **眼睛**：观察棍子的倾斜角度和倾斜速度 → **反馈系统**
- **大脑**：判断手的移动方向和速度 → **PID控制器**  
- **手**：执行移动指令 → **执行机构（电机）**

### 3.2 直立环控制分析

#### 3.2.1 作用对象
在平衡小车的直立环控制中，<span style="color:red">**作用对象是驱动小车轮子转动的"电机"**</span>。

**工作原理：**
- 当车身向前倾倒时，电机驱动轮子<span style="color:red">**向前**</span>加速，追上倒下的重心
- 当车身向后倾倒时，电机驱动轮子<span style="color:red">**向后**</span>加速，跟上重心位置
- 控制系统最终下达的指令是控制电机的<span style="color:red">**转速和方向**</span>

#### 3.2.2 反馈系统
**反馈是"小车当前的倾斜状态"**，由传感器测量提供：

1. **倾斜角度（Angle）**：小车相对于竖直地面的倾斜度数
2. **倾斜角速度（Angular Velocity）**：小车倾倒或回正的速度

**传感器组成：**
- **加速度计**：静态或慢速运动时测量角度
- **陀螺仪**：灵敏测量角速度
- **融合算法**：卡尔曼滤波或互补滤波融合数据

#### 3.2.3 闭环控制流程
1. **设定目标**：倾斜角度为0度（竖直）
2. **获取反馈**：IMU测量当前倾斜角度
3. **计算误差**：目标角度 - 当前角度
4. **PID控制计算**：
   - **P（比例）**：根据误差大小直接控制
   - **I（积分）**：累积小误差，消除静态偏差
   - **D（微分）**：根据角速度提供阻尼
5. **驱动电机**：PWM信号控制电机转速和方向
6. **状态改变**：电机转动影响小车角度
7. **循环反馈**：高频率（几百到上千次/秒）闭环控制

### 3.3 速度环控制分析

#### 3.3.1 核心思想
让小车前进的正确方法：<span style="color:red">**让小车"受控地"向前倾斜一个微小角度**</span>

**工作机制：**
- 速度环给直立环一个<span style="color:red">"向前倾斜"</span>的目标角度
- 直立环检测到前倾，驱动电机向前转动"扶正"
- 由于速度环持续提供倾斜目标，小车持续前进
- <span style="color:red">倾斜角度大小决定前进速度</span>

#### 3.3.2 作用对象
速度环的<span style="color:red">**作用对象是"直立环的目标角度（Setpoint）"**</span>

- <span style="color:red">速度环不直接控制电机</span>
- 通过修改直立环的目标角度间接控制运动
- 形成串级PID控制结构

#### 3.3.3 反馈系统
**反馈是"小车当前的实际速度"**

- 通过电机编码器测量转速
- 结合车轮周长计算线速度
- 提供实时速度反馈信息

#### 3.3.4 串级控制流程
1. **设定目标**：用户设定目标速度
2. **获取反馈**：编码器测量实际速度
3. **计算误差**：目标速度 - 实际速度
4. **速度环PID**：计算目标倾角
5. **传递给内环**：目标倾角发送给直立环
6. **内环工作**：直立环控制电机实现倾角
7. **速度改变**：小车速度随之调整
8. **循环控制**：直到达到目标速度

## 4. 控制系统架构图

```mermaid
graph TD
    %% 速度环 (外环)
    subgraph SpeedLoop ["速度环 - 外环, 控制速度"]
        TargetSpeed["用户设定的目标速度"] --> SpeedSum{ }
        ActualSpeed["反馈: 实际速度"] --> |负反馈| PID_V["速度环 PID 控制器"]
        PID_V -- 速度误差 --> SpeedSum
        SpeedSum -- 计算出 --> TargetAngle["目标倾角"]
    end

    %% 直立环 (内环)
    subgraph BalanceLoop ["直立环 - 内环, 控制直立"]
        TargetAngle --> AngleSum{ }
        ActualAngle["反馈: 实际角度"] --> |负反馈| PID_A["直立环 PID 控制器"]
        PID_A -- 角度误差 --> AngleSum
        AngleSum -- 计算出 --> MotorPWM["电机控制值 PWM"]
    end

    %% 物理系统
    subgraph PhysicalSystem ["物理系统 - 小车本体"]
        MotorPWM --> Motors["电机"]
        Motors --> CarBody["车身与车轮"]
        CarBody -- 物理状态 --> IMU["IMU 传感器"]
        CarBody -- 物理状态 --> Encoder["编码器"]
        IMU --> ActualAngle
        Encoder --> ActualSpeed
    end

    %% 设定节点形状
    style SpeedSum fill:#dae8fc,stroke:#6c8ebf,stroke-width:2px
    style AngleSum fill:#dae8fc,stroke:#6c8ebf,stroke-width:2px
    style TargetAngle fill:#d5e8d4,stroke:#82b366,stroke-width:2px
```

## 5. 直立环详细分析

### 5.1 为什么直立环用PD控制？

直立环如同杂技演员走钢丝，需要**瞬间反应**和**抑制抖动**：

#### 5.1.1 P（比例）项作用
- **提供恢复力**：倾斜角度越大，恢复力越强
- **基础控制**：如弹簧效应，是最重要的控制分量
- **快速响应**：立即对倾斜做出反应

#### 5.1.2 D（微分）项作用  
- **提供阻尼**：关注倾斜速度（角速度）
- **防止过冲**：快速倾倒时提供制动力
- **抑制振荡**：防止来回晃动，提供稳定性

**PD组合效果**：反应迅速、动作干脆、稳定不抖

### 5.2 为什么不用I项？

在直立环中使用I项会造成严重问题：

#### 5.2.1 积分延迟导致振荡
- I项累积历史误差，产生滞后效应
- 当车身回正时，I项仍带有"历史包袱"
- 导致过冲和持续振荡

#### 5.2.2 积分饱和问题
- 小车倒地时I项疯狂累积误差
- 扶正时释放巨大积分值
- 导致电机飞转，无法恢复平衡

#### 5.2.3 与速度环冲突
- 速度环给出目标倾角（如2度）
- I项认为这是需要消除的"静态误差"
- 两个环节产生控制冲突

### 5.3 直立环使用角速度而非误差微分

#### 5.3.1 理论等价性
```
D项 = Kd × d(error)/dt
error = 目标角度 - 实际角度 = 0 - 实际角度
因此：D项 = -Kd × d(实际角度)/dt = -Kd × 角速度
```

陀螺仪直接测量角速度，理论上完全等价。

#### 5.3.2 实践优势
1. **避免噪声放大**：微分运算会放大传感器噪声
2. **实时性更好**：陀螺仪提供当前时刻最新数据
3. **计算效率高**：直接使用，无需额外计算
4. **信号更平滑**：比计算得到的微分信号更稳定

#### 5.3.3 代码实现示例
```c
static float calculate_balance_pd_with_gyro(PID_T * _tpPID, float _current_angle, float _current_gyro)
{
    float error, p_out, d_out, output;

    // 1. 计算误差 (Error)
    error = _tpPID->target - _current_angle;

    // 2. 计算比例项 (P)
    p_out = _tpPID->kp * error;

    // 3. 计算微分项 (D) - 直接使用角速度
    d_out = _tpPID->kd * _current_gyro;

    // 4. 计算总输出
    output = p_out + d_out;

    // 5. 输出限幅 
    if(output > _tpPID->limit)
        output = _tpPID->limit;
    else if(output < -_tpPID->limit)
        output = -_tpPID->limit;
        
    return output;
}
```

## 6. 速度环详细分析

### 6.1 为什么速度环使用PI控制？

**参数关系：Ki = (1/200) × Kp，仅调Kp即可**

速度环的核心目标是**精确达到并保持设定速度**：

#### 6.1.1 P（比例）项作用
- **快速响应**：根据速度误差成比例输出目标倾角
- **大致控制**：让小车接近目标速度
- **存在稳态误差**：无法完全消除速度偏差

#### 6.1.2 I（积分）项作用
- **消除稳态误差**：累积微小速度偏差
- **精确控制**：确保最终精确达到目标速度
- **克服阻力**：补偿摩擦力等恒定干扰

### 6.2 为什么不用D项？

1. **噪声敏感**：速度信号微分会放大编码器噪声
2. **响应变慢**：抑制快速变化，影响加速性能  
3. **内环已稳定**：直立环PD已提供足够稳定性

### 6.3 船长与舵手比喻

- **船长（速度环-PI）**：制定航行目标，用P下达方向指令，用I修正偏差
- **舵手（直立环-PD）**：执行指令，用P保持方向，用D抵消干扰

## 7. 参数整定与极性判断

### 7.1 机械中值确定

由于小车重心可能偏离，需要找到平衡角度：

**测定方法：**
1. 小车放在地面上
2. 从前向后和从后向前绕电机轴旋转
3. 记录两次倒下的角度
4. 取中值作为机械中值

**示例：** 向前倒在2度，向后倒在-3度，机械中值为-0.5度

### 7.2 直立环参数整定

#### 7.2.1 Kp极性判断
- **极性错误**：小车往哪边倒，车轮往反方向转，加速倾倒
- **极性正确**：小车往哪边倒，车轮往同方向转，产生直立趋势

#### 7.2.2 Kd极性判断
**实践方法：**
1. 暂时设Kp=0，Kd为较小正值
2. 用手扶着小车慢慢向前倾倒
3. 观察现象：
   - 车轮向前转阻止倾倒 → Kd极性正确
   - 车轮向后转加速倾倒 → Kd极性错误，改为负值

**重要提醒：** Kp和Kd的符号不一定相同！

### 7.3 速度环参数整定

1. **先调直立环**：给Kp和Kd赋值，实现基本平衡
2. **速度环极性测试**：
   - 假设Kp极性为正，给较小值
   - Kp和Ki极性相同
   - 测试：小车前倾时车轮加速转动 → 极性正确
   - 否则：小车在平衡位置车轮几乎不转 → 极性错误

### 7.4 参数调节参考
详细调参方法可参考视频教程：
https://www.bilibili.com/video/BV1zo4y1D7bx/

## 8. 系统特点总结

### 8.1 控制特点
- **双环串级控制**：内环快速稳定，外环精确控制
- **高频响应**：控制周期达到几百到上千Hz
- **传感器融合**：IMU和编码器协同工作

### 8.2 关键技术点
- 直立环使用PD控制，直接使用陀螺仪角速度
- 速度环使用PI控制，通过目标倾角间接控制
- 机械中值补偿和参数极性判断至关重要

### 8.3 调试要点
- 先确保硬件连接正确
- 按顺序调节：机械中值 → 直立环 → 速度环
- 注意参数极性，避免正反馈导致系统不稳定

![系统架构图](./教程.assets/image-20250803160627482.png)
